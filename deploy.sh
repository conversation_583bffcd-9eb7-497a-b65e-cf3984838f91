#!/bin/bash

# GPT Researcher Docker 部署脚本
# 使用方法: ./deploy.sh [选项]
# 选项:
#   --build-only    只构建镜像，不启动服务
#   --backend-only  只启动后端服务
#   --frontend-only 只启动前端服务
#   --full         启动所有服务（默认）
#   --stop         停止所有服务
#   --clean        清理所有容器和镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装。请先安装 Docker。"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose 未安装。请先安装 Docker Compose。"
        exit 1
    fi
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            print_warning ".env 文件不存在，正在从 .env.example 创建..."
            cp .env.example .env
            print_warning "请编辑 .env 文件并添加您的 API 密钥："
            print_warning "  - DEEPSEEK_API_KEY: 您的 DeepSeek API 密钥"
            print_warning "  - TAVILY_API_KEY: 您的 Tavily API 密钥"
            print_warning "编辑完成后，请重新运行此脚本。"
            exit 1
        else
            print_error ".env.example 文件不存在。请确保您在正确的目录中。"
            exit 1
        fi
    fi
    
    # 检查必需的环境变量
    source .env
    if [ -z "$DEEPSEEK_API_KEY" ] || [ "$DEEPSEEK_API_KEY" = "your_deepseek_api_key_here" ]; then
        print_error "请在 .env 文件中设置 DEEPSEEK_API_KEY"
        exit 1
    fi
    
    if [ -z "$TAVILY_API_KEY" ] || [ "$TAVILY_API_KEY" = "your_tavily_api_key_here" ]; then
        print_error "请在 .env 文件中设置 TAVILY_API_KEY"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    print_step "创建必要的目录..."
    mkdir -p my-docs
    mkdir -p outputs
    mkdir -p logs
    print_message "目录创建完成"
}

# 构建镜像
build_images() {
    print_step "构建 Docker 镜像..."
    
    # 使用 docker compose 或 docker-compose
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
    
    $COMPOSE_CMD build --no-cache
    print_message "镜像构建完成"
}

# 启动服务
start_services() {
    local services="$1"
    print_step "启动服务: $services"
    
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
    
    if [ -z "$services" ]; then
        $COMPOSE_CMD up -d gpt-researcher gptr-nextjs
    else
        $COMPOSE_CMD up -d $services
    fi
    
    print_message "服务启动完成"
    print_message "后端服务: http://localhost:8000"
    print_message "前端服务: http://localhost:3000"
}

# 停止服务
stop_services() {
    print_step "停止所有服务..."
    
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
    
    $COMPOSE_CMD down
    print_message "服务已停止"
}

# 清理容器和镜像
clean_all() {
    print_step "清理所有容器和镜像..."
    
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
    
    $COMPOSE_CMD down --rmi all --volumes --remove-orphans
    print_message "清理完成"
}

# 显示日志
show_logs() {
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
    
    $COMPOSE_CMD logs -f
}

# 显示帮助信息
show_help() {
    echo "GPT Researcher Docker 部署脚本"
    echo ""
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --build-only     只构建镜像，不启动服务"
    echo "  --backend-only   只启动后端服务"
    echo "  --frontend-only  只启动前端服务"
    echo "  --full          启动所有服务（默认）"
    echo "  --stop          停止所有服务"
    echo "  --clean         清理所有容器和镜像"
    echo "  --logs          显示服务日志"
    echo "  --help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 启动所有服务"
    echo "  $0 --backend-only     # 只启动后端"
    echo "  $0 --stop             # 停止所有服务"
    echo "  $0 --clean            # 清理所有内容"
}

# 主函数
main() {
    print_message "GPT Researcher Docker 部署脚本"
    
    case "${1:-}" in
        --help)
            show_help
            exit 0
            ;;
        --stop)
            check_docker
            stop_services
            exit 0
            ;;
        --clean)
            check_docker
            clean_all
            exit 0
            ;;
        --logs)
            check_docker
            show_logs
            exit 0
            ;;
        --build-only)
            check_docker
            check_env_file
            create_directories
            build_images
            exit 0
            ;;
        --backend-only)
            check_docker
            check_env_file
            create_directories
            build_images
            start_services "gpt-researcher"
            exit 0
            ;;
        --frontend-only)
            check_docker
            check_env_file
            create_directories
            build_images
            start_services "gptr-nextjs"
            exit 0
            ;;
        --full|"")
            check_docker
            check_env_file
            create_directories
            build_images
            start_services
            exit 0
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
