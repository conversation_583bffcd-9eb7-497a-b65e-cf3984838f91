# GPT Researcher Docker 部署指南

本指南将帮助您使用Docker快速部署GPT Researcher项目。

## 📋 前置要求

### 1. 安装Docker
- **Windows**: 下载并安装 [Docker Desktop for Windows](https://docs.docker.com/desktop/install/windows-install/)
- **macOS**: 下载并安装 [Docker Desktop for Mac](https://docs.docker.com/desktop/install/mac-install/)
- **Linux**: 安装 [Docker Engine](https://docs.docker.com/engine/install/) 和 [Docker Compose](https://docs.docker.com/compose/install/)

### 2. 获取API密钥
您需要以下API密钥：

#### 必需的API密钥：
- **OpenAI API Key**: 
  - 访问 [OpenAI Platform](https://platform.openai.com/api-keys)
  - 创建新的API密钥
  
- **Tavily API Key**: 
  - 访问 [Tavily](https://tavily.com/)
  - 注册并获取API密钥

#### 可选的API密钥：
- **LangChain API Key**: 用于追踪和监控（可选）
  - 访问 [LangSmith](https://smith.langchain.com/)

## 🚀 快速开始

### 方法1: 使用自动化脚本（推荐）

#### Linux/macOS:
```bash
# 1. 克隆项目
git clone https://github.com/assafelovic/gpt-researcher.git
cd gpt-researcher

# 2. 给脚本执行权限
chmod +x deploy.sh

# 3. 运行部署脚本
./deploy.sh
```

#### Windows:
```cmd
# 1. 克隆项目
git clone https://github.com/assafelovic/gpt-researcher.git
cd gpt-researcher

# 2. 运行部署脚本
deploy.bat
```

### 方法2: 手动部署

#### 1. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，添加您的API密钥
nano .env  # 或使用您喜欢的编辑器
```

在 `.env` 文件中设置：
```env
OPENAI_API_KEY=your_openai_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here
LANGCHAIN_API_KEY=your_langchain_api_key_here  # 可选
```

#### 2. 创建必要目录
```bash
mkdir -p my-docs outputs logs
```

#### 3. 构建和启动服务
```bash
# 构建镜像
docker compose build

# 启动所有服务
docker compose up -d
```

## 🎯 部署选项

### 完整部署（后端 + 前端）
```bash
# 使用脚本
./deploy.sh --full

# 或手动
docker compose up -d gpt-researcher gptr-nextjs
```

### 仅后端服务
```bash
# 使用脚本
./deploy.sh --backend-only

# 或手动
docker compose up -d gpt-researcher
```

### 仅前端服务
```bash
# 使用脚本
./deploy.sh --frontend-only

# 或手动
docker compose up -d gptr-nextjs
```

## 🔧 服务访问

部署成功后，您可以通过以下地址访问服务：

- **后端API**: http://localhost:8000
- **前端界面**: http://localhost:3000
- **API文档**: http://localhost:8000/docs

## 📊 监控和管理

### 查看服务状态
```bash
docker compose ps
```

### 查看日志
```bash
# 查看所有服务日志
docker compose logs -f

# 查看特定服务日志
docker compose logs -f gpt-researcher
docker compose logs -f gptr-nextjs
```

### 停止服务
```bash
# 使用脚本
./deploy.sh --stop

# 或手动
docker compose down
```

### 重启服务
```bash
docker compose restart
```

## 🗂️ 文件和目录结构

部署后会创建以下目录：

```
gpt-researcher/
├── my-docs/          # 本地文档存储
├── outputs/          # 研究报告输出
├── logs/            # 应用日志
├── .env             # 环境变量配置
├── deploy.sh        # Linux/macOS部署脚本
├── deploy.bat       # Windows部署脚本
└── docker-compose.yml
```

## ⚙️ 高级配置

### 自定义端口
编辑 `docker-compose.yml` 文件：

```yaml
services:
  gpt-researcher:
    ports:
      - "8080:8000"  # 将后端端口改为8080
  
  gptr-nextjs:
    ports:
      - "3001:3000"  # 将前端端口改为3001
```

### 环境变量配置
在 `.env` 文件中可以配置更多选项：

```env
# 基本配置
OPENAI_API_KEY=your_key_here
TAVILY_API_KEY=your_key_here

# 高级配置
RETRIEVER=tavily
EMBEDDING_PROVIDER=openai
LLM_PROVIDER=openai
TEMPERATURE=0.4
MAX_SEARCH_RESULTS_PER_QUERY=5

# 本地文档
DOC_PATH=./my-docs

# 日志配置
LOGGING_LEVEL=INFO
```

## 🧪 测试部署

### 1. 健康检查
```bash
# 检查后端API
curl http://localhost:8000/health

# 检查前端
curl http://localhost:3000
```

### 2. 运行测试
```bash
# 运行内置测试
docker compose --profile test up gpt-researcher-tests
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用
netstat -tulpn | grep :8000
netstat -tulpn | grep :3000

# 停止占用端口的进程或修改docker-compose.yml中的端口配置
```

#### 2. API密钥错误
- 检查 `.env` 文件中的API密钥是否正确
- 确保API密钥有足够的配额

#### 3. 内存不足
```bash
# 增加Docker内存限制
# 在Docker Desktop设置中调整内存分配
```

#### 4. 网络问题
```bash
# 重建网络
docker compose down
docker network prune
docker compose up -d
```

### 日志分析
```bash
# 查看详细错误日志
docker compose logs --tail=100 gpt-researcher

# 查看容器状态
docker compose ps -a
```

## 🧹 清理和维护

### 清理未使用的资源
```bash
# 使用脚本清理
./deploy.sh --clean

# 或手动清理
docker compose down --rmi all --volumes --remove-orphans
docker system prune -a
```

### 更新镜像
```bash
# 拉取最新代码
git pull

# 重新构建镜像
docker compose build --no-cache

# 重启服务
docker compose up -d
```

## 📚 更多资源

- [官方文档](https://docs.gptr.dev)
- [GitHub仓库](https://github.com/assafelovic/gpt-researcher)
- [Discord社区](https://discord.gg/QgZXvJAccX)

## 🆘 获取帮助

如果遇到问题，可以：

1. 查看本文档的故障排除部分
2. 检查项目的GitHub Issues
3. 在Discord社区寻求帮助
4. 提交新的Issue到GitHub仓库
