# 必需的API密钥
DEEPSEEK_API_KEY=your_deepseek_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here

# 可选的API密钥
LANGCHAIN_API_KEY=your_langchain_api_key_here
OPENAI_API_KEY=your_openai_api_key_here  # 可选，用于embedding

# 应用配置
DOC_PATH=./my-docs
LOGGING_LEVEL=INFO

# 前端配置
NEXT_PUBLIC_GPTR_API_URL=http://localhost:8000
NEXT_PUBLIC_GA_MEASUREMENT_ID=

# LLM配置 - 使用DeepSeek
FAST_LLM=deepseek:deepseek-chat
SMART_LLM=deepseek:deepseek-chat
STRATEGIC_LLM=deepseek:deepseek-chat

# Embedding配置 - 可以使用OpenAI或其他提供商
EMBEDDING=openai:text-embedding-3-small

# 其他可选配置
RETRIEVER=tavily
TEMPERATURE=0.4
MAX_SEARCH_RESULTS_PER_QUERY=5

# 已弃用的配置（保留兼容性）
EMBEDDING_PROVIDER=openai
LLM_PROVIDER=deepseek