# 🐳 GPT Researcher Docker 部署完整指南

## 📁 项目文件说明

我已经为您创建了以下文件来简化Docker部署过程：

### 🔧 部署脚本
- **`deploy.sh`** - Linux/macOS自动化部署脚本
- **`deploy.bat`** - Windows自动化部署脚本

### 📖 文档
- **`DOCKER_DEPLOYMENT.md`** - 详细的Docker部署指南
- **`QUICK_START.md`** - 快速启动指南
- **`README_DOCKER.md`** - 本文件，部署总览

### ⚙️ 配置文件
- **`.env.example`** - 环境变量模板（已更新）
- **`.env`** - 您的环境变量配置文件（已创建）

## 🚀 立即开始部署

### 第一步：配置API密钥

编辑 `.env` 文件，替换以下内容：

```env
# 将这些替换为您的实际API密钥
OPENAI_API_KEY=sk-your-actual-openai-key-here
TAVILY_API_KEY=tvly-your-actual-tavily-key-here
```

### 第二步：运行部署

#### Windows用户：
```cmd
deploy.bat
```

#### Linux/macOS用户：
```bash
./deploy.sh
```

### 第三步：访问应用

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000

## 🎯 部署选项

| 命令 | Windows | Linux/macOS | 说明 |
|------|---------|-------------|------|
| 完整部署 | `deploy.bat` | `./deploy.sh` | 启动前端+后端 |
| 仅后端 | `deploy.bat --backend-only` | `./deploy.sh --backend-only` | 只启动API服务 |
| 仅前端 | `deploy.bat --frontend-only` | `./deploy.sh --frontend-only` | 只启动Web界面 |
| 停止服务 | `deploy.bat --stop` | `./deploy.sh --stop` | 停止所有服务 |
| 查看日志 | `deploy.bat --logs` | `./deploy.sh --logs` | 实时查看日志 |
| 清理环境 | `deploy.bat --clean` | `./deploy.sh --clean` | 删除所有容器和镜像 |

## 📊 服务架构

```
┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │
│   (NextJS)      │◄──►│   (FastAPI)     │
│   Port: 3000    │    │   Port: 8000    │
└─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   Static Files  │    │   API Services  │
│   - HTML/CSS/JS │    │   - Research    │
│   - Assets      │    │   - Reports     │
└─────────────────┘    │   - Documents   │
                       └─────────────────┘
```

## 🔍 验证部署

### 1. 检查服务状态
```bash
docker compose ps
```

### 2. 测试API
```bash
curl http://localhost:8000/health
```

### 3. 访问前端
打开浏览器访问 http://localhost:3000

## 📂 目录结构

```
gpt-researcher/
├── 📁 my-docs/              # 本地文档存储
├── 📁 outputs/              # 研究报告输出
├── 📁 logs/                 # 应用日志
├── 📄 .env                  # 环境变量配置
├── 📄 .env.example          # 环境变量模板
├── 🔧 deploy.sh             # Linux/macOS部署脚本
├── 🔧 deploy.bat            # Windows部署脚本
├── 📖 DOCKER_DEPLOYMENT.md  # 详细部署指南
├── 📖 QUICK_START.md        # 快速启动指南
├── 📖 README_DOCKER.md      # 本文件
└── 🐳 docker-compose.yml    # Docker编排配置
```

## ⚡ 性能优化

### 资源配置
- **内存**: 建议至少4GB
- **CPU**: 建议至少2核心
- **磁盘**: 建议至少10GB可用空间

### Docker设置
在Docker Desktop中调整资源分配：
1. 打开Docker Desktop
2. 进入Settings → Resources
3. 调整Memory和CPU限制

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. 端口冲突
```bash
# 查看端口占用
netstat -tulpn | grep :8000
netstat -tulpn | grep :3000

# 解决方案：修改docker-compose.yml中的端口映射
```

#### 2. API密钥错误
- 检查`.env`文件中的API密钥格式
- 确保API密钥有效且有足够配额

#### 3. 容器启动失败
```bash
# 查看详细日志
docker compose logs gpt-researcher
docker compose logs gptr-nextjs

# 重新构建镜像
docker compose build --no-cache
```

#### 4. 内存不足
- 在Docker Desktop中增加内存分配
- 或者只启动后端服务：`deploy.bat --backend-only`

## 🔄 更新和维护

### 更新代码
```bash
# 拉取最新代码
git pull

# 重新构建和启动
deploy.bat --clean  # 清理旧环境
deploy.bat          # 重新部署
```

### 备份数据
```bash
# 备份重要目录
cp -r outputs outputs_backup
cp -r my-docs my-docs_backup
cp .env .env_backup
```

## 📞 获取帮助

如果遇到问题：

1. 📖 查看 `DOCKER_DEPLOYMENT.md` 获取详细信息
2. 🔍 检查项目的 [GitHub Issues](https://github.com/assafelovic/gpt-researcher/issues)
3. 💬 加入 [Discord社区](https://discord.gg/QgZXvJAccX)
4. 📧 联系作者：<EMAIL>

## 🎉 开始使用

1. 确保API密钥已正确配置
2. 运行部署脚本
3. 访问 http://localhost:3000
4. 开始您的AI研究之旅！

---

**祝您使用愉快！** 🚀
