<!DOCTYPE html>
<html lang="en">

<head>
    <title>GP<PERSON> Researcher</title>
    <meta name="description" content="A research assistant powered by GPT-4">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="icon" href="./static/favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="/site/styles.css" />
    <style>
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
        }

        .agent-name {
            text-align: center;
        }

        .agent-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .agent-choices {
            display: none;
        }

        .btn-show {
            display: none;
        }

        /* Icon button style for inline buttons */
        .icon-button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 5px;
            margin-left: 5px;
            border-radius: 4px;
            transition: background-color 0.2s, color 0.2s;
        }

        .icon-button:hover {
            background-color: rgba(123, 104, 238, 0.1); /* Placeholder, adjust in styles.css */
        }

        .icon-button:active {
        }

        /* Ensure buttons are properly aligned in headings */
        h2 .icon-button, h2 .expand-button {
            vertical-align: middle;
            font-size: 0.8em;
        }
        
        /* New navigation buttons in the top-right corner */
        .nav-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 100;
        }
    </style>
</head>

<body>
    <!-- Navigation Buttons (moved from top bar) -->
    <div class="nav-buttons">
        <div id="websocketPanelOpenBtn" class="top-websocket-button">
            <i class="fas fa-network-wired"></i> Status
        </div>
        <div id="historyPanelOpenBtn" class="top-history-button">
            <i class="fas fa-history"></i> History
        </div>
    </div>

    <!-- WebSocket Status Panel -->
    <div class="websocket-panel" id="websocketPanel">
        <div class="websocket-panel-header">
            <h3><i class="fas fa-plug"></i> Connection Status</h3>
            <div class="websocket-panel-actions">
                <button id="websocketPanelToggle" class="websocket-action-btn" title="Close panel">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
        </div>
        <div class="websocket-status">
            <div class="status-item">
                <span class="status-label">Connection:</span>
                <span class="status-value" id="connectionStatus">Disconnected</span>
                <span class="status-indicator" id="connectionIndicator"></span>
            </div>
            <div class="status-item">
                <span class="status-label">Research:</span>
                <span class="status-value" id="researchStatus">Inactive</span>
            </div>
            <div class="status-item">
                <span class="status-label">Connected for:</span>
                <span class="status-value" id="connectionDuration">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">Last activity:</span>
                <span class="status-value" id="lastActivity">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">ReadyState:</span>
                <span class="status-value" id="readyState">-</span>
            </div>
            <div class="status-divider"></div>
            <div class="status-item">
                <span class="status-label">Connection attempts:</span>
                <span class="status-value" id="connectionAttempts">0</span>
            </div>
            <div class="status-item">
                <span class="status-label">Messages received:</span>
                <span class="status-value" id="messagesReceived">0</span>
            </div>
            <div class="status-divider"></div>
            <div class="status-item">
                <span class="status-label">Current task:</span>
                <span class="status-value" id="currentTask">-</span>
            </div>
        </div>
    </div>

    <section class="landing">
        <div class="max-w-5xl mx-auto text-center">
            <h1 class="text-4xl font-extrabold mx-auto lg:text-7xl">
                Say Goodbye to <br>
                <span
                    style="background-image:linear-gradient(to right, #9867F0, #ED4E50); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">Hours
                    of Research</span>
            </h1>
            <p class="max-w-6xl mx-auto text-gray-600 mt-8" style="font-size:20px">
                Say Hello to <b>GPT Researcher</b>, your AI mate for rapid insights and comprehensive research. <br>
                GPT Researcher takes care of everything from accurate source gathering and organization of research results to generation of customized reports with citations.
            </p>
            <a href="#form" class="btn btn-primary">Start Researching</a>
        </div>
    </section>

    <main class="container" id="form">
        <div class="agent-item"><img src="/static/gptr-logo.png" class="avatar" alt="Auto Agent"></div>
        <form method="POST" class="mt-3" id="researchForm">
            <div class="form-group">
                <label for="task" class="agent-question">What would you like me to research next?</label>
                <textarea id="task" name="task" class="form-control highlight-connection" placeholder="Enter any topic, question, or idea..." required autocomplete="on"></textarea>
                <input type="radio" name="agent" id="autoAgent" value="Auto Agent" checked hidden>
            </div>
            <div class="form-group">
                <div class="row">


                </div>
                <button type="button" id="btnShowAuto" class="btn btn-secondary mt-3 btn-show">Auto Agent</button>
            </div>
            <div class="form-group">
                <label for="report_type" class="agent-question">What type of report would you like me to
                    generate?</label>
                <select name="report_type" id="report_type" class="form-control highlight-connection" required>
                    <option value="research_report">Summary - Short and fast (~2 min)</option>
                    <option value="detailed_report">Detailed - In depth and longer (~5 min)</option>
                    <option value="resource_report">Resource Report</option>
                    <option value="deep">Deep Research</option>
                </select>
            </div>
            <div class="form-group">
                <label for="tone" class="agent-question">In which tone would you like the report to be
                    generated?</label>
                <select name="tone" id="tone" class="form-control highlight-connection" required>
                    <option value="Objective">Objective - Impartial and unbiased presentation of facts and findings
                    </option>
                    <option value="Formal">Formal - Adheres to academic standards with sophisticated language and
                        structure</option>
                    <option value="Analytical">Analytical - Critical evaluation and detailed examination of data and
                        theories</option>
                    <option value="Persuasive">Persuasive - Convincing the audience of a particular viewpoint or
                        argument</option>
                    <option value="Informative">Informative - Providing clear and comprehensive information on a topic
                    </option>
                    <option value="Explanatory">Explanatory - Clarifying complex concepts and processes</option>
                    <option value="Descriptive">Descriptive - Detailed depiction of phenomena, experiments, or case
                        studies</option>
                    <option value="Critical">Critical - Judging the validity and relevance of the research and its
                        conclusions</option>
                    <option value="Comparative">Comparative - Juxtaposing different theories, data, or methods to
                        highlight differences and similarities</option>
                    <option value="Speculative">Speculative - Exploring hypotheses and potential implications or future
                        research directions</option>
                    <option value="Reflective">Reflective - Considering the research process and personal insights or
                        experiences</option>
                    <option value="Narrative">Narrative - Telling a story to illustrate research findings or
                        methodologies</option>
                    <option value="Humorous">Humorous - Light-hearted and engaging, usually to make the content more
                        relatable</option>
                    <option value="Optimistic">Optimistic - Highlighting positive findings and potential benefits
                    </option>
                    <option value="Pessimistic">Pessimistic - Focusing on limitations, challenges, or negative outcomes
                    </option>
                </select>
            </div>
            <div class="form-group">
                <label for="report_source" class="agent-question">What sources would you like me to research
                    from?</label>
                <p class="text-left mt-0 pt-0" style="font-size: 0.7rem;">You can now do research on local documents as
                    well. Please make sure to add the DOC_PATH env variable pointing to your documents folder.</p>
                <select name="report_source" id="report_source" class="form-control highlight-connection" required>
                    <option value="web">The Web</option>
                    <option value="local">My Documents</option>
                    <option value="hybrid">Hybrid</option>
                    <option value="azure">Azure storage</option>
                </select>
            </div>
            <div class="form-group">
                <label for="queryDomains" class="form-label">Query Domains (Optional)</label>
                <input type="text" class="form-control highlight-connection" id="queryDomains" name="query_domains" placeholder="Enter domains separated by commas" autocomplete="on">
                <small class="text-muted">Example: techcrunch.com, forbes.com</small>
            </div>

            <!-- MCP Configuration Section -->
            <div class="form-group">
                <div class="mcp-section">
                    <div class="mcp-header">
                        <label for="mcpEnabled" class="form-label">
                            <input type="checkbox" id="mcpEnabled" name="mcp_enabled" class="mcp-toggle">
                            Enable MCP (Model Context Protocol)
                        </label>
                        <button type="button" id="mcpInfoBtn" class="mcp-info-btn" title="Learn about MCP">
                            <i class="fas fa-info-circle"></i>
                        </button>
                    </div>
                    <small class="text-muted">Connect to external tools and data sources through MCP servers</small>
                    
                    <div id="mcpConfigSection" class="mcp-config-section" style="display: none;">
                        <div class="mcp-presets">
                            <label class="form-label">Quick Presets</label>
                            <div class="preset-buttons">
                                <button type="button" class="btn btn-outline-secondary btn-sm preset-btn" data-preset="github">
                                    <i class="fab fa-github"></i> GitHub
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm preset-btn" data-preset="tavily">
                                    <i class="fas fa-search"></i> Tavily Web Search
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm preset-btn" data-preset="filesystem">
                                    <i class="fas fa-folder"></i> Local Files
                                </button>
                            </div>
                            <small class="text-muted">Click a preset to add pre-configured MCP servers to the JSON below</small>
                        </div>

                        <div class="mcp-config-group">
                            <label for="mcpConfig" class="form-label">MCP Servers Configuration</label>
                            <textarea id="mcpConfig" name="mcp_config" class="form-control mcp-config-textarea" rows="8" placeholder="Paste your MCP servers configuration as JSON array...">[]</textarea>
                            <div class="mcp-config-status">
                                <span id="mcpConfigStatus" class="mcp-status-text">Valid JSON ✓</span>
                                <button type="button" id="mcpFormatBtn" class="btn btn-sm btn-secondary">
                                    <i class="fas fa-code"></i> Format JSON
                                </button>
                            </div>
                            <small class="text-muted">
                                Paste your MCP servers configuration as a JSON array. Each server should have properties like 
                                <code>name</code>, <code>command</code>, <code>args</code>, and optional <code>env</code> variables.
                                <a href="#" id="mcpExampleLink">See example →</a>
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <input type="submit" value="Begin Research" class="btn btn-primary button-padding" id="submitButton">
        </form>

        <!-- Add JSON button above Research Progress section -->
        <div class="margin-div" id="jsonButtonContainer" style="display: none; text-align: right; margin-bottom: 10px;">
            <a id="downloadLinkJsonTop" href="#" class="report-action-btn disabled" target="_blank" rel="noopener noreferrer">
                <i class="fas fa-file-code"></i> JSON
            </a>
        </div>

        <div class="margin-div research-output-container">
            <!-- Move spinner to the left side of the text -->
            <h2><div id="modernSpinner" class="modern-spinner"></div> Research Progress <button id="expandOutputBtn" class="expand-button" title="Expand"><i class="fas fa-expand-alt"></i></button></h2>
            <p class="mt-2 text-left" style="font-size: 0.8rem;">
                Watch as the AI works to gather information and analyze your topic in real-time.</p>
            <div id="output"></div>
        </div>
        <div class="images_div">
            <div id="selectedImagesContainer" style="display: none;"></div>
        </div>
        <div class="margin-div report-container">
            <h2>Research Report
                <button id="copyToClipboardTop" class="icon-button" title="Copy" style="display: none;">
                    <i class="fas fa-copy"></i>
                </button>
                <button id="expandReportBtn" class="expand-button" title="Expand">
                    <i class="fas fa-expand-alt"></i>
                </button>
            </h2>
            <!-- Add download buttons above the report container -->
            <div class="report-actions" style="display: none;">
                <a id="downloadLinkTop" href="#" class="report-action-btn disabled" target="_blank" rel="noopener noreferrer">
                    <i class="fas fa-file-pdf"></i> PDF
                </a>
                <a id="downloadLinkWordTop" href="#" class="report-action-btn disabled" target="_blank" rel="noopener noreferrer">
                    <i class="fas fa-file-word"></i> Word
                </a>
                <a id="downloadLinkMdTop" href="#" class="report-action-btn disabled" target="_blank" rel="noopener noreferrer">
                    <i class="fas fa-file-lines"></i> Markdown
                </a>
            </div>
            <div id="reportContainer"></div>
            <div id="reportActions" style="display: none;">
                <div class="alert alert-info" role="alert" id="status"></div>
            </div>
        </div>

        <!-- Chat Container -->
        <div class="margin-div chat-container" id="chatContainer" style="display: none;">
            <h2><i class="fas fa-comments"></i> Chat with AI about this research <button id="expandChatBtn" class="expand-button" title="Expand"><i class="fas fa-expand-alt"></i></button></h2>
            <p class="text-muted">Ask questions about the research report to get more insights</p>
            <div id="chatMessages" class="chat-messages"></div>
            <div class="chat-input-container">
                <textarea id="chatInput" class="form-control chat-input" placeholder="Ask a question about this research..." rows="2"></textarea>
                <button id="voiceInputBtn" class="btn btn-secondary" title="Use voice input">
                    <i class="fas fa-microphone"></i>
                </button>
                <button id="sendChatBtn" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i> Send
                </button>
            </div>
        </div>

        <!-- Fixed bottom bar styled like the top credits bar -->
    </main>

    <!-- Conversation History Panel -->
    <div class="history-panel" id="historyPanel">
        <div class="history-panel-header">
            <h3><i class="fas fa-history"></i> Research History</h3>
            <div class="history-panel-actions">
                <button id="historyPanelToggle" class="history-action-btn" title="Close panel">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="history-panel-search">
            <input type="text" id="historySearch" placeholder="Search research history...">
            <button id="historySearchBtn" class="history-action-btn" title="Search">
                <i class="fas fa-search"></i>
            </button>
        </div>
        <div class="history-panel-filters">
            <select id="historySortOrder">
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
            </select>
            <!-- JS will add Import/Export/Debug buttons here -->
            <button id="historyClearBtn" class="history-action-btn" title="Clear all history">
                <i class="fas fa-trash-alt"></i>
            </button>
        </div>
        <div class="history-panel-entries" id="historyEntries">
            <!-- Entries will be populated dynamically -->
        </div>
    </div>
    
    <!-- Sticky Downloads Bar -->
    <div class="sticky-downloads-bar" id="stickyDownloadsBar" style="display: none;"> <!-- Initially hidden -->
        <div class="download-buttons-container">
            <a id="copyToClipboard" class="download-option-btn disabled">
                <i class="fas fa-copy"></i> Copy (Markdown)
            </a>
            <a id="downloadLinkMd" href="#" class="download-option-btn disabled" target="_blank" rel="noopener noreferrer">
                <i class="fas fa-file-lines"></i> Markdown
            </a>
            <a id="downloadLink" href="#" class="download-option-btn disabled" target="_blank" rel="noopener noreferrer">
                <i class="fas fa-file-pdf"></i> PDF
            </a>
            <a id="downloadLinkWord" href="#" class="download-option-btn disabled" target="_blank" rel="noopener noreferrer">
                <i class="fas fa-file-word"></i> Word
            </a>
            <a id="downloadLinkJson" href="#" class="download-option-btn disabled" target="_blank" rel="noopener noreferrer">
                <i class="fas fa-file-code"></i> Log (JSON)
            </a>
        </div>
    </div>

    <footer>
        <p>
            <a target="_blank" href="https://gptr.dev">Homepage</a> |
            <a target="_blank" href="https://github.com/assafelovic/gpt-researcher">GitHub</a> |
            <a target="_blank" href="https://discord.gg/spBgZmm3Xe">Discord</a>
        </p>
        <p>GPT Researcher &copy; 2024</p>
    </footer>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/showdown/1.9.1/showdown.min.js"></script>
    <script src="/site/scripts.js"></script>
    <script>
        // Auto-resize textarea as content grows
        const taskTextarea = document.getElementById('task');
        if (taskTextarea) {
            // Set initial height
            taskTextarea.setAttribute('style', 'height: 38px; overflow-y: hidden;');

            // Function to resize textarea based on content
            const resizeTextarea = () => {
                taskTextarea.style.height = 'auto';
                taskTextarea.style.height = taskTextarea.scrollHeight + 'px';
            };

            // Add event listeners for input and focus
            taskTextarea.addEventListener('input', resizeTextarea);
            taskTextarea.addEventListener('focus', resizeTextarea);
        }

        // Ensure feature panels are positioned correctly on window resize
        window.addEventListener('resize', function () {
            // Adjust the feature panel width based on screen size
            const viewportWidth = window.innerWidth;
            const featurePanel = document.querySelector('.feature-panel');

            if (featurePanel) {
                if (viewportWidth < 1400) {
                    featurePanel.style.display = 'none';
                } else {
                    featurePanel.style.display = 'block';
                    // Adjust width based on screen size
                    const panelWidth = Math.min(280, Math.max(200, viewportWidth * 0.15));
                    featurePanel.style.width = `${panelWidth}px`;
                }
            }
        });
    </script>
</body>

</html>
