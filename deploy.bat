@echo off
setlocal enabledelayedexpansion

REM GPT Researcher Docker 部署脚本 (Windows版本)
REM 使用方法: deploy.bat [选项]

set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:print_message
echo %GREEN%[INFO]%NC% %~1
goto :eof

:print_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:print_error
echo %RED%[ERROR]%NC% %~1
goto :eof

:print_step
echo %BLUE%[STEP]%NC% %~1
goto :eof

:check_docker
docker --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker 未安装。请先安装 Docker Desktop。"
    exit /b 1
)

docker compose version >nul 2>&1
if errorlevel 1 (
    docker-compose --version >nul 2>&1
    if errorlevel 1 (
        call :print_error "Docker Compose 未安装。请先安装 Docker Compose。"
        exit /b 1
    )
    set "COMPOSE_CMD=docker-compose"
) else (
    set "COMPOSE_CMD=docker compose"
)
goto :eof

:check_env_file
if not exist ".env" (
    if exist ".env.example" (
        call :print_warning ".env 文件不存在，正在从 .env.example 创建..."
        copy ".env.example" ".env" >nul
        call :print_warning "请编辑 .env 文件并添加您的 API 密钥："
        call :print_warning "  - OPENAI_API_KEY: 您的 OpenAI API 密钥"
        call :print_warning "  - TAVILY_API_KEY: 您的 Tavily API 密钥"
        call :print_warning "编辑完成后，请重新运行此脚本。"
        pause
        exit /b 1
    ) else (
        call :print_error ".env.example 文件不存在。请确保您在正确的目录中。"
        exit /b 1
    )
)

REM 简单检查环境变量是否设置
findstr /C:"OPENAI_API_KEY=" .env | findstr /V /C:"OPENAI_API_KEY=$" | findstr /V /C:"OPENAI_API_KEY=your_openai_api_key_here" >nul
if errorlevel 1 (
    call :print_error "请在 .env 文件中设置 OPENAI_API_KEY"
    exit /b 1
)

findstr /C:"TAVILY_API_KEY=" .env | findstr /V /C:"TAVILY_API_KEY=$" | findstr /V /C:"TAVILY_API_KEY=your_tavily_api_key_here" >nul
if errorlevel 1 (
    call :print_error "请在 .env 文件中设置 TAVILY_API_KEY"
    exit /b 1
)
goto :eof

:create_directories
call :print_step "创建必要的目录..."
if not exist "my-docs" mkdir "my-docs"
if not exist "outputs" mkdir "outputs"
if not exist "logs" mkdir "logs"
call :print_message "目录创建完成"
goto :eof

:build_images
call :print_step "构建 Docker 镜像..."
%COMPOSE_CMD% build --no-cache
if errorlevel 1 (
    call :print_error "镜像构建失败"
    exit /b 1
)
call :print_message "镜像构建完成"
goto :eof

:start_services
set "services=%~1"
call :print_step "启动服务: %services%"

if "%services%"=="" (
    %COMPOSE_CMD% up -d gpt-researcher gptr-nextjs
) else (
    %COMPOSE_CMD% up -d %services%
)

if errorlevel 1 (
    call :print_error "服务启动失败"
    exit /b 1
)

call :print_message "服务启动完成"
call :print_message "后端服务: http://localhost:8000"
call :print_message "前端服务: http://localhost:3000"
goto :eof

:stop_services
call :print_step "停止所有服务..."
%COMPOSE_CMD% down
call :print_message "服务已停止"
goto :eof

:clean_all
call :print_step "清理所有容器和镜像..."
%COMPOSE_CMD% down --rmi all --volumes --remove-orphans
call :print_message "清理完成"
goto :eof

:show_logs
%COMPOSE_CMD% logs -f
goto :eof

:show_help
echo GPT Researcher Docker 部署脚本 (Windows版本)
echo.
echo 使用方法: %~nx0 [选项]
echo.
echo 选项:
echo   --build-only     只构建镜像，不启动服务
echo   --backend-only   只启动后端服务
echo   --frontend-only  只启动前端服务
echo   --full          启动所有服务（默认）
echo   --stop          停止所有服务
echo   --clean         清理所有容器和镜像
echo   --logs          显示服务日志
echo   --help          显示此帮助信息
echo.
echo 示例:
echo   %~nx0                    # 启动所有服务
echo   %~nx0 --backend-only     # 只启动后端
echo   %~nx0 --stop             # 停止所有服务
echo   %~nx0 --clean            # 清理所有内容
goto :eof

:main
call :print_message "GPT Researcher Docker 部署脚本"

if "%1"=="--help" (
    call :show_help
    exit /b 0
)

if "%1"=="--stop" (
    call :check_docker
    call :stop_services
    exit /b 0
)

if "%1"=="--clean" (
    call :check_docker
    call :clean_all
    exit /b 0
)

if "%1"=="--logs" (
    call :check_docker
    call :show_logs
    exit /b 0
)

if "%1"=="--build-only" (
    call :check_docker
    call :check_env_file
    call :create_directories
    call :build_images
    exit /b 0
)

if "%1"=="--backend-only" (
    call :check_docker
    call :check_env_file
    call :create_directories
    call :build_images
    call :start_services "gpt-researcher"
    exit /b 0
)

if "%1"=="--frontend-only" (
    call :check_docker
    call :check_env_file
    call :create_directories
    call :build_images
    call :start_services "gptr-nextjs"
    exit /b 0
)

if "%1"=="--full" goto :full_deploy
if "%1"=="" goto :full_deploy

call :print_error "未知选项: %1"
call :show_help
exit /b 1

:full_deploy
call :check_docker
call :check_env_file
call :create_directories
call :build_images
call :start_services
exit /b 0

call :main %*
