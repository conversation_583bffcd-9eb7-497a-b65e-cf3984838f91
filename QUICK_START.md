# 🚀 GPT Researcher Docker 快速启动

## 📋 准备工作

### 1. 获取API密钥
在开始之前，您需要获取以下API密钥：

#### 必需：
- **DeepSeek API Key**: 访问 https://platform.deepseek.com/api_keys
- **Tavily API Key**: 访问 https://tavily.com/

#### 可选：
- **OpenAI API Key**: 访问 https://platform.openai.com/api-keys (用于embedding)
- **LangChain API Key**: 访问 https://smith.langchain.com/

### 2. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，替换您的API密钥
# Windows用户可以使用记事本: notepad .env
# Linux/macOS用户可以使用: nano .env 或 vim .env
```

在 `.env` 文件中，将以下内容替换为您的实际API密钥：
```env
OPENAI_API_KEY=sk-your-actual-openai-key-here
TAVILY_API_KEY=tvly-your-actual-tavily-key-here
```

## 🎯 一键部署

### Windows用户：
```cmd
deploy.bat
```

### Linux/macOS用户：
```bash
./deploy.sh
```

## 🌐 访问应用

部署完成后，打开浏览器访问：

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 🔧 其他命令

### 只启动后端：
```bash
# Windows
deploy.bat --backend-only

# Linux/macOS
./deploy.sh --backend-only
```

### 只启动前端：
```bash
# Windows
deploy.bat --frontend-only

# Linux/macOS
./deploy.sh --frontend-only
```

### 停止服务：
```bash
# Windows
deploy.bat --stop

# Linux/macOS
./deploy.sh --stop
```

### 查看日志：
```bash
# Windows
deploy.bat --logs

# Linux/macOS
./deploy.sh --logs
```

### 清理所有内容：
```bash
# Windows
deploy.bat --clean

# Linux/macOS
./deploy.sh --clean
```

## ❓ 遇到问题？

1. **端口被占用**: 修改 `docker-compose.yml` 中的端口配置
2. **API密钥错误**: 检查 `.env` 文件中的密钥是否正确
3. **内存不足**: 在Docker Desktop中增加内存分配
4. **网络问题**: 运行 `docker compose down && docker compose up -d`

更多详细信息请查看 `DOCKER_DEPLOYMENT.md` 文件。

## 🎉 开始使用

1. 打开 http://localhost:3000
2. 输入您想要研究的主题
3. 点击"开始研究"
4. 等待AI生成详细的研究报告

享受使用GPT Researcher！
