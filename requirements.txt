aiofiles>=23.2.1
aiohappyeyeballs>=2.6.1
aiohttp>=3.12.0
aiosignal>=1.3.2
annotated-types>=0.7.0
anyio>=4.9.0
arxiv>=2.0.0
attrs>=25.3.0
beautifulsoup4>=4.12.2
brotli>=1.1.0
certifi>=2025.4.26
cffi>=1.17.1
chardet>=5.2.0
charset-normalizer>=3.4.2
click>=8.2.1
colorama>=0.4.6
cryptography>=45.0.2
cssselect2>=0.8.0
dataclasses-json>=0.6.7
distro>=1.9.0
docopt>=0.6.2
duckduckgo-search>=4.1.1
fastapi>=0.104.1
feedparser>=6.0.11
filelock>=3.18.0
filetype>=1.2.0
fonttools>=4.58.0
frozenlist>=1.6.0
fsspec>=2025.5.1
greenlet>=3.2.2
h11>=0.16.0
htmldocx>=0.0.6
httpcore>=1.0.9
httpx>=0.28.1
httpx-aiohttp>=0.1.4
httpx-sse>=0.4.0
huggingface-hub>=0.32.0
idna>=3.10
importlib-metadata>=8.7.0
jinja2>=3.1.6
jiter>=0.10.0
joblib>=1.5.1
json-repair>=0.29.8
json5>=0.9.25
jsonpatch>=1.33
jsonpointer>=3.0.0
jsonschema>=4.23.0
jsonschema-specifications>=2025.4.1
kiwisolver>=1.4.8
langchain-community>=0.3.17
langchain-core>=0.3.61
langchain-ollama>=0.3.3
langchain-openai>=0.3.6
langchain-text-splitters>=0.3.8
langgraph>=0.2.76
langgraph-checkpoint>=2.0.26
langgraph-cli>=0.2.10
langgraph-sdk>=0.1.70
langsmith>=0.3.42
litellm>=1.71.0
loguru>=0.7.2
lxml>=4.9.2
markdown>=3.5.1
markdown2>=2.5.3
markupsafe>=3.0.2
marshmallow>=3.26.1
mcp>=1.9.1
md2pdf>=1.0.1
mistune>=3.0.2
multidict>=6.4.4
mypy-extensions>=1.1.0
nest-asyncio>=1.6.0
numpy>=2.2.6
olefile>=0.47
ollama>=0.4.8
openai>=1.3.3
orjson>=3.10.18
ormsgpack>=1.10.0
packaging>=24.2
pillow>=11.2.1
primp>=0.15.0
propcache>=0.3.1
psutil>=7.0.0
pycparser>=2.22
pydantic>=2.5.1
pydantic-core>=2.33.2
pydantic-settings>=2.9.1
pydyf>=0.11.0
pymupdf>=1.23.6
python-docx>=1.1.0
python-dotenv>=1.0.0
python-multipart>=0.0.6
pyyaml>=6.0.1
rapidfuzz>=3.13.0
referencing>=0.36.2
regex>=2024.11.6
requests>=2.31.0
requests-toolbelt>=1.0.0
rpds-py>=0.25.1
sgmllib3k>=1.0.0
six>=1.17.0
sniffio>=1.3.1
soupsieve>=2.7
sqlalchemy>=2.0.28
sse-starlette>=2.3.5
starlette>=0.46.2
tenacity>=9.1.2
tiktoken>=0.7.0
tinycss2>=1.4.0
tinyhtml5>=2.0.0
tokenizers>=0.21.1
tqdm>=4.67.1
typing-extensions>=4.13.2
typing-inspect>=0.9.0
typing-inspection>=0.4.1
unstructured>=0.13
unstructured-client>=0.35.0
urllib3>=2.4.0
uvicorn>=0.24.0.post1
webencodings>=0.5.1
websockets>=13.1
win32-setctime>=1.2.0
wrapt>=1.17.2
yarl>=1.20.0
zipp>=3.21.0
zopfli>=0.2.3.post1
zstandard>=0.23.0
# Model Context Protocol support (optional)
mcp>=1.0.0
langchain-mcp-adapters>=0.1.0  # LangChain MCP adapters for tool integration
